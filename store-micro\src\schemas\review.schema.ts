import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ReviewDocument = Review & Document;

@Schema({ timestamps: true })
export class Review {
  @Prop({ required: true, min: 1, max: 5 })
  rating: number;

  @Prop({ required: true })
  comment: string;

  @Prop({
    type: {
      name: { type: String, required: true },
      email: { type: String, required: true },
    },
    required: true,
  })
  customer: {
    name: string;
    email: string;
  };

  @Prop()
  reply: string;

  @Prop({ required: true })
  storeId: string;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);
