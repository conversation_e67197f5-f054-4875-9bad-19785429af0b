import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Order, OrderDocument, OrderStatus } from '../schemas/order.schema';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { PaginationDto, PaginatedResponse } from '../common/dto/pagination.dto';

@Injectable()
export class OrdersService {
  constructor(
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
  ) {}

  async create(createOrderDto: CreateOrderDto): Promise<Order> {
    const createdOrder = new this.orderModel(createOrderDto);
    return createdOrder.save();
  }

  async findAll(
    storeId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<Order>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.orderModel
        .find({ storeId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.orderModel.countDocuments({ storeId }),
    ]);

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, storeId: string): Promise<Order> {
    const order = await this.orderModel.findOne({ _id: id, storeId }).exec();
    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }
    return order;
  }

  async updateStatus(
    id: string,
    storeId: string,
    updateOrderStatusDto: UpdateOrderStatusDto,
  ): Promise<Order> {
    const updatedOrder = await this.orderModel
      .findOneAndUpdate(
        { _id: id, storeId },
        { status: updateOrderStatusDto.status },
        { new: true },
      )
      .exec();

    if (!updatedOrder) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return updatedOrder;
  }

  async getOrderStats(storeId: string) {
    const stats = await this.orderModel.aggregate([
      { $match: { storeId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
        },
      },
    ]);

    const result = {
      pending: 0,
      processing: 0,
      ready: 0,
      delivered: 0,
      cancelled: 0,
      totalRevenue: 0,
    };

    stats.forEach((stat) => {
      result[stat._id] = stat.count;
      if (stat._id === OrderStatus.DELIVERED) {
        result.totalRevenue = stat.totalAmount;
      }
    });

    return result;
  }

  async countByStore(storeId: string): Promise<number> {
    return this.orderModel.countDocuments({ storeId });
  }

  async countPendingByStore(storeId: string): Promise<number> {
    return this.orderModel.countDocuments({
      storeId,
      status: { $in: [OrderStatus.PENDING, OrderStatus.PROCESSING] },
    });
  }

  async countCompletedByStore(storeId: string): Promise<number> {
    return this.orderModel.countDocuments({
      storeId,
      status: OrderStatus.DELIVERED,
    });
  }

  async getTotalRevenueByStore(storeId: string): Promise<number> {
    const result = await this.orderModel.aggregate([
      { $match: { storeId, status: OrderStatus.DELIVERED } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } },
    ]);

    return result.length > 0 ? result[0].total : 0;
  }
}
