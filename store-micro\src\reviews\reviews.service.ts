import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Review, ReviewDocument } from '../schemas/review.schema';
import { CreateReviewDto } from './dto/create-review.dto';
import { ReplyReviewDto } from './dto/reply-review.dto';
import { PaginationDto, PaginatedResponse } from '../common/dto/pagination.dto';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
  ) {}

  async create(createReviewDto: CreateReviewDto): Promise<Review> {
    const createdReview = new this.reviewModel(createReviewDto);
    return createdReview.save();
  }

  async findAll(
    storeId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<Review>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.reviewModel
        .find({ storeId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.reviewModel.countDocuments({ storeId }),
    ]);

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, storeId: string): Promise<Review> {
    const review = await this.reviewModel.findOne({ _id: id, storeId }).exec();
    if (!review) {
      throw new NotFoundException(`Review with ID ${id} not found`);
    }
    return review;
  }

  async reply(
    id: string,
    storeId: string,
    replyReviewDto: ReplyReviewDto,
  ): Promise<Review> {
    const updatedReview = await this.reviewModel
      .findOneAndUpdate(
        { _id: id, storeId },
        { reply: replyReviewDto.reply },
        { new: true },
      )
      .exec();

    if (!updatedReview) {
      throw new NotFoundException(`Review with ID ${id} not found`);
    }

    return updatedReview;
  }

  async getAverageRating(storeId: string): Promise<number> {
    const result = await this.reviewModel.aggregate([
      { $match: { storeId } },
      { $group: { _id: null, averageRating: { $avg: '$rating' } } },
    ]);

    return result.length > 0 ? Math.round(result[0].averageRating * 10) / 10 : 0;
  }

  async countByStore(storeId: string): Promise<number> {
    return this.reviewModel.countDocuments({ storeId });
  }
}
