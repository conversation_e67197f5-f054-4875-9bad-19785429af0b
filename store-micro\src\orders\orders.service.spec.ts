import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { OrdersService } from './orders.service';
import { Order, OrderStatus } from '../schemas/order.schema';

describe('OrdersService', () => {
  let service: OrdersService;
  let mockOrderModel: any;

  const mockOrder = {
    _id: '507f1f77bcf86cd799439012',
    products: [
      {
        productId: '507f1f77bcf86cd799439011',
        name: 'Test Product',
        price: 25.99,
        quantity: 2,
      },
    ],
    totalAmount: 51.98,
    status: OrderStatus.PENDING,
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+**********',
      address: '123 Main St',
    },
    storeId: 'store123',
    save: jest.fn().mockResolvedValue(this),
  };

  beforeEach(async () => {
    mockOrderModel = {
      new: jest.fn().mockResolvedValue(mockOrder),
      constructor: jest.fn().mockResolvedValue(mockOrder),
      find: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      countDocuments: jest.fn(),
      aggregate: jest.fn(),
      exec: jest.fn(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      sort: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrdersService,
        {
          provide: getModelToken(Order.name),
          useValue: mockOrderModel,
        },
      ],
    }).compile();

    service = module.get<OrdersService>(OrdersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create an order', async () => {
      const createOrderDto = {
        products: [
          {
            productId: '507f1f77bcf86cd799439011',
            name: 'Test Product',
            price: 25.99,
            quantity: 2,
          },
        ],
        totalAmount: 51.98,
        customer: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+**********',
          address: '123 Main St',
        },
        storeId: 'store123',
      };

      mockOrderModel.save = jest.fn().mockResolvedValue(mockOrder);
      jest.spyOn(mockOrderModel, 'constructor').mockImplementation(() => ({
        save: mockOrderModel.save,
      }));

      const result = await service.create(createOrderDto as any);
      expect(result).toEqual(mockOrder);
    });
  });

  describe('getTotalRevenueByStore', () => {
    it('should return total revenue for delivered orders', async () => {
      const storeId = 'store123';
      const mockAggregateResult = [{ _id: null, total: 1000 }];

      mockOrderModel.aggregate.mockResolvedValue(mockAggregateResult);

      const result = await service.getTotalRevenueByStore(storeId);
      expect(result).toBe(1000);
      expect(mockOrderModel.aggregate).toHaveBeenCalledWith([
        { $match: { storeId, status: OrderStatus.DELIVERED } },
        { $group: { _id: null, total: { $sum: '$totalAmount' } } },
      ]);
    });

    it('should return 0 when no delivered orders exist', async () => {
      const storeId = 'store123';
      mockOrderModel.aggregate.mockResolvedValue([]);

      const result = await service.getTotalRevenueByStore(storeId);
      expect(result).toBe(0);
    });
  });
});
