import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type SupplierDocument = Supplier & Document;

@Schema({ timestamps: true })
export class Supplier {
  @Prop({ required: true })
  name: string;

  @Prop({
    type: {
      email: { type: String, required: true },
      phone: { type: String, required: true },
      address: { type: String, required: true },
    },
    required: true,
  })
  contact: {
    email: string;
    phone: string;
    address: string;
  };

  @Prop({ type: [String], default: [] })
  products: string[];

  @Prop({ min: 0, max: 5, default: 0 })
  rating: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ required: true })
  storeId: string;
}

export const SupplierSchema = SchemaFactory.createForClass(Supplier);
