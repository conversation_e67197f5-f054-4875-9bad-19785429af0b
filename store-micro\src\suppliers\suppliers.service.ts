import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Supplier, SupplierDocument } from '../schemas/supplier.schema';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { PaginationDto, PaginatedResponse } from '../common/dto/pagination.dto';

@Injectable()
export class SuppliersService {
  constructor(
    @InjectModel(Supplier.name) private supplierModel: Model<SupplierDocument>,
  ) {}

  async create(createSupplierDto: CreateSupplierDto): Promise<Supplier> {
    const createdSupplier = new this.supplierModel(createSupplierDto);
    return createdSupplier.save();
  }

  async findAll(
    storeId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<Supplier>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.supplierModel
        .find({ storeId })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.supplierModel.countDocuments({ storeId }),
    ]);

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, storeId: string): Promise<Supplier> {
    const supplier = await this.supplierModel.findOne({ _id: id, storeId }).exec();
    if (!supplier) {
      throw new NotFoundException(`Supplier with ID ${id} not found`);
    }
    return supplier;
  }

  async update(
    id: string,
    storeId: string,
    updateSupplierDto: UpdateSupplierDto,
  ): Promise<Supplier> {
    const updatedSupplier = await this.supplierModel
      .findOneAndUpdate(
        { _id: id, storeId },
        updateSupplierDto,
        { new: true },
      )
      .exec();

    if (!updatedSupplier) {
      throw new NotFoundException(`Supplier with ID ${id} not found`);
    }

    return updatedSupplier;
  }

  async remove(id: string, storeId: string): Promise<void> {
    const result = await this.supplierModel
      .deleteOne({ _id: id, storeId })
      .exec();

    if (result.deletedCount === 0) {
      throw new NotFoundException(`Supplier with ID ${id} not found`);
    }
  }

  async countByStore(storeId: string): Promise<number> {
    return this.supplierModel.countDocuments({ storeId, isActive: true });
  }
}
