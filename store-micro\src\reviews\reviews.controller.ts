import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ReplyReviewDto } from './dto/reply-review.dto';
import { PaginationDto } from '../common/dto/pagination.dto';

@ApiTags('reviews')
@Controller('reviews')
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new review' })
  @ApiResponse({ status: 201, description: 'Review created successfully' })
  create(@Body() createReviewDto: CreateReviewDto) {
    return this.reviewsService.create(createReviewDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all reviews for a store' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Reviews retrieved successfully' })
  findAll(
    @Query('storeId') storeId: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return this.reviewsService.findAll(storeId, paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a review by ID' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Review retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Review not found' })
  findOne(@Param('id') id: string, @Query('storeId') storeId: string) {
    return this.reviewsService.findOne(id, storeId);
  }

  @Patch(':id/reply')
  @ApiOperation({ summary: 'Reply to a review' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Reply added successfully' })
  @ApiResponse({ status: 404, description: 'Review not found' })
  reply(
    @Param('id') id: string,
    @Query('storeId') storeId: string,
    @Body() replyReviewDto: ReplyReviewDto,
  ) {
    return this.reviewsService.reply(id, storeId, replyReviewDto);
  }

  @Get('stats/average-rating')
  @ApiOperation({ summary: 'Get average rating for a store' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Average rating retrieved successfully' })
  getAverageRating(@Query('storeId') storeId: string) {
    return this.reviewsService.getAverageRating(storeId);
  }
}
