import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors();

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Global prefix
  app.setGlobalPrefix('api/store');

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Store Microservice API')
    .setDescription('API for managing store operations')
    .setVersion('1.0')
    .addTag('dashboard', 'Dashboard statistics')
    .addTag('products', 'Product management')
    .addTag('orders', 'Order management')
    .addTag('suppliers', 'Supplier management')
    .addTag('reviews', 'Review management')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(3001);
  console.log('Store Microservice is running on http://localhost:3001');
  console.log('API Documentation available at http://localhost:3001/api/docs');
}

bootstrap();
