# Store Microservice

A NestJS-based microservice for managing store operations including products, orders, suppliers, reviews, and dashboard analytics.

## Features

- **Products Management**: CRUD operations for store products
- **Orders Management**: Order tracking and status updates
- **Suppliers Management**: Supplier information and relationships
- **Reviews Management**: Customer reviews and store replies
- **Dashboard**: Comprehensive store statistics and analytics
- **MongoDB Integration**: Using Mongoose for data persistence
- **API Documentation**: Swagger/OpenAPI documentation
- **Data Validation**: Using class-validator for request validation
- **Pagination**: Built-in pagination for large datasets

## Tech Stack

- **Framework**: NestJS with TypeScript
- **Database**: MongoDB with Mongoose
- **Validation**: class-validator and class-transformer
- **Documentation**: Swagger/OpenAPI
- **Testing**: Jest

## Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Make sure MongoDB is running on your local machine or update the connection string in `src/app.module.ts`

4. Start the development server:
```bash
npm run start:dev
```

The API will be available at `http://localhost:3001/api/store`

## API Documentation

Once the server is running, you can access the Swagger documentation at:
`http://localhost:3001/api/docs`

## API Endpoints

### Dashboard
- `GET /dashboard?storeId={storeId}` - Get store statistics

### Products
- `GET /products?storeId={storeId}` - List all products
- `POST /products` - Create a new product
- `GET /products/{id}?storeId={storeId}` - Get product by ID
- `PATCH /products/{id}?storeId={storeId}` - Update product
- `DELETE /products/{id}?storeId={storeId}` - Delete product

### Orders
- `GET /orders?storeId={storeId}` - List all orders
- `POST /orders` - Create a new order
- `GET /orders/{id}?storeId={storeId}` - Get order by ID
- `PATCH /orders/{id}/status?storeId={storeId}` - Update order status
- `GET /orders/stats/summary?storeId={storeId}` - Get order statistics

### Suppliers
- `GET /suppliers?storeId={storeId}` - List all suppliers
- `POST /suppliers` - Create a new supplier
- `GET /suppliers/{id}?storeId={storeId}` - Get supplier by ID
- `PATCH /suppliers/{id}?storeId={storeId}` - Update supplier
- `DELETE /suppliers/{id}?storeId={storeId}` - Delete supplier

### Reviews
- `GET /reviews?storeId={storeId}` - List all reviews
- `POST /reviews` - Create a new review
- `GET /reviews/{id}?storeId={storeId}` - Get review by ID
- `PATCH /reviews/{id}/reply?storeId={storeId}` - Reply to a review
- `GET /reviews/stats/average-rating?storeId={storeId}` - Get average rating

## Data Models

### Product
- name, description, price, stock, category
- image, tags, deliveryOptions, nutritionalInfo
- isActive, storeId

### Order
- products (array), totalAmount, status, customer
- storeId, timestamps

### Supplier
- name, contact (email, phone, address)
- products, rating, isActive, storeId

### Review
- rating (1-5), comment, customer
- reply, storeId, timestamps

## Testing

Run unit tests:
```bash
npm run test
```

Run tests with coverage:
```bash
npm run test:cov
```

## Environment Variables

You can create a `.env` file to configure:
- `MONGODB_URI`: MongoDB connection string
- `PORT`: Server port (default: 3001)

## Store ID Parameter

All endpoints require a `storeId` parameter to filter data by store. This ensures data isolation between different stores using the same microservice instance.

## Pagination

List endpoints support pagination with query parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

## Error Handling

The API returns appropriate HTTP status codes and error messages:
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `500`: Internal Server Error
