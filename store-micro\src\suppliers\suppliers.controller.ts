import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { SuppliersService } from './suppliers.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { PaginationDto } from '../common/dto/pagination.dto';

@ApiTags('suppliers')
@Controller('suppliers')
export class SuppliersController {
  constructor(private readonly suppliersService: SuppliersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new supplier' })
  @ApiResponse({ status: 201, description: 'Supplier created successfully' })
  create(@Body() createSupplierDto: CreateSupplierDto) {
    return this.suppliersService.create(createSupplierDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all suppliers for a store' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Suppliers retrieved successfully' })
  findAll(
    @Query('storeId') storeId: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return this.suppliersService.findAll(storeId, paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a supplier by ID' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Supplier retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Supplier not found' })
  findOne(@Param('id') id: string, @Query('storeId') storeId: string) {
    return this.suppliersService.findOne(id, storeId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a supplier' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Supplier updated successfully' })
  @ApiResponse({ status: 404, description: 'Supplier not found' })
  update(
    @Param('id') id: string,
    @Query('storeId') storeId: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
  ) {
    return this.suppliersService.update(id, storeId, updateSupplierDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a supplier' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Supplier deleted successfully' })
  @ApiResponse({ status: 404, description: 'Supplier not found' })
  remove(@Param('id') id: string, @Query('storeId') storeId: string) {
    return this.suppliersService.remove(id, storeId);
  }
}
