import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>N<PERSON>ber,
  IsString,
  IsEmail,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

class CustomerDto {
  @ApiProperty({ example: '<PERSON>' })
  @IsString()
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;
}

export class CreateReviewDto {
  @ApiProperty({ example: 5, minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ example: 'Great service and quality products!' })
  @IsString()
  comment: string;

  @ApiProperty({ type: CustomerDto })
  @ValidateNested()
  @Type(() => CustomerDto)
  customer: CustomerDto;

  @ApiProperty({ example: 'store123' })
  @IsString()
  storeId: string;
}
