import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { DashboardService } from './dashboard.service';

@ApiTags('dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  @ApiOperation({ summary: 'Get dashboard statistics for a store' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalProducts: { type: 'number', example: 150 },
        totalOrders: { type: 'number', example: 1250 },
        pendingOrders: { type: 'number', example: 25 },
        completedOrders: { type: 'number', example: 1200 },
        totalRevenue: { type: 'number', example: 45000.50 },
        averageRating: { type: 'number', example: 4.5 },
        totalSuppliers: { type: 'number', example: 12 },
        totalReviews: { type: 'number', example: 340 },
      },
    },
  })
  getStats(@Query('storeId') storeId: string) {
    return this.dashboardService.getStats(storeId);
  }
}
