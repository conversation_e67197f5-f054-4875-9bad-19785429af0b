import { Injectable } from '@nestjs/common';
import { ProductsService } from '../products/products.service';
import { OrdersService } from '../orders/orders.service';
import { SuppliersService } from '../suppliers/suppliers.service';
import { ReviewsService } from '../reviews/reviews.service';

export interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageRating: number;
  totalSuppliers: number;
  totalReviews: number;
}

@Injectable()
export class DashboardService {
  constructor(
    private readonly productsService: ProductsService,
    private readonly ordersService: OrdersService,
    private readonly suppliersService: SuppliersService,
    private readonly reviewsService: ReviewsService,
  ) {}

  async getStats(storeId: string): Promise<DashboardStats> {
    const [
      totalProducts,
      totalOrders,
      pendingOrders,
      completedOrders,
      totalRevenue,
      averageRating,
      totalSuppliers,
      totalReviews,
    ] = await Promise.all([
      this.productsService.countByStore(storeId),
      this.ordersService.countByStore(storeId),
      this.ordersService.countPendingByStore(storeId),
      this.ordersService.countCompletedByStore(storeId),
      this.ordersService.getTotalRevenueByStore(storeId),
      this.reviewsService.getAverageRating(storeId),
      this.suppliersService.countByStore(storeId),
      this.reviewsService.countByStore(storeId),
    ]);

    return {
      totalProducts,
      totalOrders,
      pendingOrders,
      completedOrders,
      totalRevenue,
      averageRating,
      totalSuppliers,
      totalReviews,
    };
  }
}
