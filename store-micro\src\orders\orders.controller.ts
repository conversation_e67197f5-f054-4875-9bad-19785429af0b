import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { PaginationDto } from '../common/dto/pagination.dto';

@ApiTags('orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({ status: 201, description: 'Order created successfully' })
  create(@Body() createOrderDto: CreateOrderDto) {
    return this.ordersService.create(createOrderDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all orders for a store' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully' })
  findAll(
    @Query('storeId') storeId: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return this.ordersService.findAll(storeId, paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an order by ID' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Order retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  findOne(@Param('id') id: string, @Query('storeId') storeId: string) {
    return this.ordersService.findOne(id, storeId);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update order status' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Order status updated successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  updateStatus(
    @Param('id') id: string,
    @Query('storeId') storeId: string,
    @Body() updateOrderStatusDto: UpdateOrderStatusDto,
  ) {
    return this.ordersService.updateStatus(id, storeId, updateOrderStatusDto);
  }

  @Get('stats/summary')
  @ApiOperation({ summary: 'Get order statistics for a store' })
  @ApiQuery({ name: 'storeId', required: true, example: 'store123' })
  @ApiResponse({ status: 200, description: 'Order stats retrieved successfully' })
  getStats(@Query('storeId') storeId: string) {
    return this.ordersService.getOrderStats(storeId);
  }
}
