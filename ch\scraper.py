from playwright.sync_api import sync_playwright
import json
import time

# Configuración
CONFIG = {
    "url": "https://www.tripadvisor.com/Restaurant_Review-g1096406-d2092454-Reviews-High_House_Farm_Brewery-Matfen_Northumberland_England.html",
    "selectors": [
        'div[data-automation="reviewCard"]',
        '[data-test-target="review-card"]',
        '.review-container',
        '.reviewSelector',
        '[data-reviewid]',
        '.review',
        '[class*="review"]',
        'div[class*="Review"]'
    ]
}

def init_browser():
    """Inicializa el navegador y configura la página"""
    playwright = sync_playwright().start()
    browser = playwright.chromium.launch(headless=False, slow_mo=1000)
    page = browser.new_page()
    
    # Configura headers para parecer más humano
    page.set_extra_http_headers({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    
    return playwright, browser, page

def prepare_page(page):
    """Prepara la página navegando y esperando contenido"""
    print('Navegando a TripAdvisor...')
    page.goto(CONFIG["url"])
    print('Esperando contenido...')
    
    # Espera y hace scroll para cargar contenido dinámico
    page.wait_for_timeout(7000)
    page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
    page.wait_for_timeout(3000)
    
    # Toma captura de pantalla
    page.screenshot(path='debug_python.png')
    print('Captura guardada como debug_python.png')

def find_review_selector(page):
    """Encuentra el selector correcto para las reseñas"""
    for selector in CONFIG["selectors"]:
        elements = page.query_selector_all(selector)
        if len(elements) > 0:
            print(f'Encontrado: {selector} con {len(elements)} elementos')
            return selector
    return None

def extract_reviews(page, selector):
    """Extrae las reseñas usando el selector encontrado"""
    def find_text(card, selectors):
        """Función helper para encontrar texto con múltiples selectores"""
        for sel in selectors:
            element = card.query_selector(sel)
            if element and element.text_content() and element.text_content().strip():
                return element.text_content().strip()
        return "No encontrado"
    
    # Obtiene todas las tarjetas de reseñas
    cards = page.query_selector_all(selector)
    reviews = []
    
    for index, card in enumerate(cards):
        # Selectores para cada campo
        author_selectors = [
            'div.QIHsu.Zb span.biGQs._P.fiohW.fOtGX a',
            '.memberOverlayLink'
        ]
        
        title_selectors = [
            'div[data-test-target="review-title"] a',
            '.noQuotes'
        ]
        
        date_selectors = [
            'div.fUmAk div.biGQs._P.pZUbB.ncFvv.osNWb',
            '.ratingDate'
        ]
        
        body_selectors = [
            'div[data-test-target="review-body"] span.ThzWO span.JguWG',
            '.partial_entry'
        ]
        
        # Extrae información
        author = find_text(card, author_selectors)
        title = find_text(card, title_selectors)
        date = find_text(card, date_selectors).replace("Written ", "")
        body = find_text(card, body_selectors)
        
        # Ubicación con selector específico
        location_element = card.query_selector('div.vYLts span.biGQs._P.pZUbB.osNWb span')
        location = location_element.text_content() if location_element else "No encontrado"
        
        review = {
            "index": index + 1,
            "author": author,
            "title": title,
            "date": date,
            "body": body,
            "location": location
        }
        
        reviews.append(review)
    
    return reviews

def scrape_reviews():
    """Función principal que ejecuta todo el proceso de scraping"""
    playwright, browser, page = init_browser()
    
    try:
        prepare_page(page)
        selector = find_review_selector(page)
        
        if not selector:
            print('No se encontraron reseñas')
            return
        
        print('Extrayendo reseñas...')
        reviews = extract_reviews(page, selector)
        
        print(f'Se encontraron {len(reviews)} reseñas:')
        print(json.dumps(reviews, indent=2, ensure_ascii=False))
        
        # Opcional: guardar en archivo JSON
        with open('reviews_python.json', 'w', encoding='utf-8') as f:
            json.dump(reviews, f, indent=2, ensure_ascii=False)
        print('Reseñas guardadas en reviews_python.json')
        
    except Exception as error:
        print(f'Error: {error}')
    finally:
        browser.close()
        playwright.stop()
        print('Navegador cerrado.')

if __name__ == "__main__":
    scrape_reviews()
