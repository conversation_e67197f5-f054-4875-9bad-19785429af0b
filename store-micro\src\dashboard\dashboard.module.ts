import { Module } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { ProductsModule } from '../products/products.module';
import { OrdersModule } from '../orders/orders.module';
import { SuppliersModule } from '../suppliers/suppliers.module';
import { ReviewsModule } from '../reviews/reviews.module';

@Module({
  imports: [ProductsModule, OrdersModule, SuppliersModule, ReviewsModule],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}
