import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { ProductsService } from './products.service';
import { Product } from '../schemas/product.schema';

describe('ProductsService', () => {
  let service: ProductsService;
  let mockProductModel: any;

  const mockProduct = {
    _id: '507f1f77bcf86cd799439011',
    name: 'Test Product',
    description: 'Test Description',
    price: 25.99,
    stock: 100,
    category: 'Test Category',
    storeId: 'store123',
    isActive: true,
    save: jest.fn().mockResolvedValue(this),
  };

  beforeEach(async () => {
    mockProductModel = {
      new: jest.fn().mockResolvedValue(mockProduct),
      constructor: jest.fn().mockResolvedValue(mockProduct),
      find: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      deleteOne: jest.fn(),
      countDocuments: jest.fn(),
      exec: jest.fn(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductsService,
        {
          provide: getModelToken(Product.name),
          useValue: mockProductModel,
        },
      ],
    }).compile();

    service = module.get<ProductsService>(ProductsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a product', async () => {
      const createProductDto = {
        name: 'Test Product',
        price: 25.99,
        storeId: 'store123',
      };

      mockProductModel.save = jest.fn().mockResolvedValue(mockProduct);
      jest.spyOn(mockProductModel, 'constructor').mockImplementation(() => ({
        save: mockProductModel.save,
      }));

      const result = await service.create(createProductDto as any);
      expect(result).toEqual(mockProduct);
    });
  });

  describe('findAll', () => {
    it('should return paginated products', async () => {
      const paginationDto = { page: 1, limit: 10 };
      const storeId = 'store123';

      mockProductModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([mockProduct]),
      });
      mockProductModel.countDocuments.mockResolvedValue(1);

      const result = await service.findAll(storeId, paginationDto);

      expect(result).toEqual({
        data: [mockProduct],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });
  });

  describe('countByStore', () => {
    it('should count products by store', async () => {
      const storeId = 'store123';
      mockProductModel.countDocuments.mockResolvedValue(5);

      const result = await service.countByStore(storeId);
      expect(result).toBe(5);
      expect(mockProductModel.countDocuments).toHaveBeenCalledWith({
        storeId,
        isActive: true,
      });
    });
  });
});
